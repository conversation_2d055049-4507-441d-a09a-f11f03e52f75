Product Requirements Document: Talent Matching Platform MVP
1. Overview

This document outlines the requirements for the Minimum Viable Product (MVP) of an intelligent Talent Matching Platform. The platform will leverage an advanced AI agent that combines Retrieval-Augmented Generation (RAG) using a vector database with the relational power of a knowledge graph. This hybrid system will analyze and match talent profiles and resumes with available projects, providing insightful recommendations and streamlining the recruitment and allocation process.

The core of the system is an AI agent capable of understanding complex queries, searching across semantic and relational data stores, and presenting a cohesive, intelligent response through a user-friendly web interface.

2. Problem Statement

Organizations struggle to efficiently map the right talent to the right projects. The process is often manual, time-consuming, and relies on keyword matching, which fails to capture the nuanced relationships between skills, experience, and project requirements. This leads to suboptimal team composition, under-utilization of internal talent, and a prolonged, inefficient hiring process for external candidates.

Recruiters & Hiring Managers spend excessive time manually screening profiles and lack tools to identify the best-fit candidates (internal or external) quickly.

Internal Talent often lacks visibility into projects that align with their career development goals and skillsets.

External Candidates have difficulty finding roles that truly match their expertise and potential within an organization.

3. Goals and Objectives

The primary goal of this MVP is to build and showcase a functional talent-matching platform that demonstrates the superiority of a hybrid AI approach (Vector DB + Knowledge Graph).

Objective 1: Implement a robust data ingestion pipeline to process project, talent, and resume data from markdown files into a PostgreSQL vector database and a Neo4j knowledge graph.

Objective 2: Develop an intelligent AI agent that can perform semantic, relational, and hybrid searches to answer queries and provide matching recommendations.

Objective 3: Create an intuitive Streamlit-based user interface for users to browse, search, and analyze project and talent data.

Objective 4: Deliver a quantitative "Profile Matching Score" to provide at-a-glance insights into a candidate's suitability for a project.

Objective 5: Ensure the entire environment is easily reproducible and deployable using Podman and taskfile.

4. Target Audience

Recruiters/HR Personnel: Users focused on sourcing and screening external candidates for open roles.

Project Managers/Team Leads: Users focused on finding the best-suited internal talent for their projects and filling team gaps.

Internal Talent (Employees): Users looking for new projects that align with their skills and career growth plans.

5. Core Features (Functional Requirements)
5.1. Data Ingestion & Processing

The system must be able to ingest and parse markdown files from three distinct directories: projects/, talent/, and resumes/.

During ingestion, the pipeline will perform semantic chunking on the documents.

It will generate vector embeddings for semantic search and store them in a PostgreSQL database with the pgvector extension.

It will extract entities (e.g., skills, technologies, companies, roles) and their relationships to build and populate a Neo4j knowledge graph.

5.2. Backend & AI Agent

The AI agent will serve as the query engine, accessible via a FastAPI backend.

The agent shall have tools to query:

The PostgreSQL vector database for semantic similarity searches (e.g., "find me talent similar to this job description").

The Neo4j knowledge graph for relational queries (e.g., "who has worked on projects with a similar tech stack?").

A hybrid search that combines results from both data stores for comprehensive answers.

The agent must be able to calculate a "Profile Matching Score" for candidate-project pairs based on skills, experience, and other defined criteria.

5.3. Technical Environment & Deployment

The environment setup, including database creation and schema migration, must be managed via taskfile.

The entire application stack (FastAPI, Streamlit, PostgreSQL, Neo4j) must be containerized and managed using Podman.

5.4. Streamlit User Interface (UI)

The UI will be organized into the following sections:

1. Projects List View

Display all projects from the projects/ directory in a list.

Each list item will show the project title, status (e.g., Active, Planning, Completed), and a short description.

Projects in the list will be clickable, navigating the user to the Project Details View.

2. Project Details View

Display the full content of the selected project's markdown file.

Clearly list project requirements, goals, and timelines.

Display a list of currently assigned team members and their roles.

Feature a distinct "Job Description" section outlining requirements for any open roles on the project, including required skills and experience level.

3. Talent List View (Internal Professionals)

Display a searchable list of all internal talent from the talent/ directory.

Each list item will show the professional's name, primary skills, and availability status.

Provide UI controls to filter the list by skills and years of experience.

4. Talent Details View

Display the full profile for a selected internal professional.

Include a "Career Planning" section structured with the OKR (Objectives and Key Results) framework.

This section will display the talent's current objectives, key results with progress tracking (e.g., progress bars), and future career goals.

5. Candidates List View (External Applicants)

Display a list of all external candidates from the resumes/ directory.

Each list item will show the candidate's name, key skills, and application status (e.g., New, Under Review, Matched).

Provide UI controls to filter candidates by skills and experience.

6. Candidate Details View

Display the full resume and extracted information for a selected candidate.

Implement a radar chart to visualize the candidate's skill proficiency in key areas relevant to the company's projects.

For each available project, calculate and display a "Profile Matching Score" as a percentage. This score should be based on an analysis of skills, experience level, and other requirements from the project's job description.

Provide a ranked list of "Best-Fit Projects" for the candidate based on their matching score.

6. Out of Scope for MVP

User authentication and role-based access control.

Ability to edit project or profile data from the UI.

Integration with external Applicant Tracking Systems (ATS) or HR platforms.

Real-time notifications or alerts.

Advanced analytics dashboards.

7. Success Metrics

The entire platform is successfully containerized with Podman and can be deployed with a single taskfile command.

All three data sources (projects, talent, resumes) are successfully ingested into both PostgreSQL and Neo4j.

The Streamlit UI successfully implements all 6 specified views.

The "Profile Matching Score" generates a relevant and logical percentage for at least 80% of candidate-project pairs in the test dataset.

The AI agent can successfully answer at least 10 different test queries that require a combination of vector and graph searches.