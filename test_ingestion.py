#!/usr/bin/env python3
"""
Test script for the data ingestion pipeline
"""
import asyncio
import os
import sys
from pathlib import Path

# Add the project root to Python path
sys.path.append(str(Path(__file__).parent))

from data_ingestion.main import DataIngestionPipeline


async def test_pipeline():
    """Test the data ingestion pipeline"""
    print("=" * 60)
    print("TALENT MATCHING PLATFORM - DATA INGESTION TEST")
    print("=" * 60)
    
    # Check if required directories exist
    required_dirs = ['projects', 'talent', 'resumes']
    for directory in required_dirs:
        if os.path.exists(directory):
            files = list(Path(directory).glob("*.txt"))
            print(f"✓ {directory}/ directory found with {len(files)} files")
        else:
            print(f"✗ {directory}/ directory not found")
    
    print("\n" + "=" * 60)
    print("TESTING DATA INGESTION PIPELINE")
    print("=" * 60)
    
    # Get Gemini API key
    gemini_api_key = os.getenv("GEMINI_API_KEY", "AIzaSyB80zE6F7ibg2c-BXFrpKWfpIljoTJoqao")
    
    try:
        # Initialize pipeline
        print("1. Initializing pipeline...")
        pipeline = DataIngestionPipeline(gemini_api_key)
        await pipeline.initialize()
        print("   ✓ Pipeline initialized successfully")
        
        # Test markdown processing
        print("\n2. Testing markdown processing...")
        if os.path.exists('talent'):
            chunks = pipeline.markdown_processor.process_directory('talent')
            print(f"   ✓ Processed {len(chunks)} chunks from talent directory")
            
            if chunks:
                sample_chunk = chunks[0]
                print(f"   ✓ Sample chunk: {sample_chunk.chunk_id}")
                print(f"   ✓ Content length: {len(sample_chunk.content)} characters")
                print(f"   ✓ Metadata keys: {list(sample_chunk.metadata.keys())}")
        
        # Test embedding generation
        print("\n3. Testing embedding generation...")
        test_text = "Senior Data Scientist with machine learning expertise"
        embedding = pipeline.embeddings_generator.generate_embedding(test_text)
        print(f"   ✓ Generated embedding with dimension: {len(embedding)}")
        
        # Test database operations (if databases are running)
        print("\n4. Testing database connections...")
        try:
            # Test a simple query to check if databases are accessible
            await pipeline.postgres_manager.connection_pool.acquire()
            print("   ✓ PostgreSQL connection successful")
        except Exception as e:
            print(f"   ✗ PostgreSQL connection failed: {str(e)}")
            print("   → Make sure to run: task postgres:start")
        
        try:
            with pipeline.neo4j_manager.driver.session() as session:
                result = session.run("RETURN 1 as test")
                result.single()
            print("   ✓ Neo4j connection successful")
        except Exception as e:
            print(f"   ✗ Neo4j connection failed: {str(e)}")
            print("   → Make sure to run: task neo4j:start")
        
        # Test search functionality (if data exists)
        print("\n5. Testing search functionality...")
        try:
            results = await pipeline.search_documents("machine learning", limit=3)
            print(f"   ✓ Search returned {len(results)} results")
            
            for i, result in enumerate(results[:2]):
                print(f"   → Result {i+1}: {result['source_file']} (score: {result['similarity_score']:.3f})")
        except Exception as e:
            print(f"   ✗ Search test failed: {str(e)}")
        
        print("\n" + "=" * 60)
        print("PIPELINE TEST COMPLETED")
        print("=" * 60)
        
        await pipeline.close()
        
    except Exception as e:
        print(f"\n✗ Pipeline test failed: {str(e)}")
        print("\nTroubleshooting:")
        print("1. Make sure databases are running:")
        print("   task postgres:start")
        print("   task neo4j:start")
        print("2. Check if all dependencies are installed:")
        print("   pip install -r requirements.txt")
        print("3. Verify Gemini API key is set correctly")


async def test_individual_components():
    """Test individual components separately"""
    print("\n" + "=" * 60)
    print("TESTING INDIVIDUAL COMPONENTS")
    print("=" * 60)
    
    # Test markdown processor
    print("1. Testing MarkdownProcessor...")
    from data_ingestion.processors.markdown_processor import MarkdownProcessor
    
    processor = MarkdownProcessor()
    if os.path.exists('talent/sarah_martinez_principal_data_scientist.txt'):
        chunks = processor.process_file('talent/sarah_martinez_principal_data_scientist.txt')
        print(f"   ✓ Processed file into {len(chunks)} chunks")
        
        if chunks:
            sample = chunks[0]
            print(f"   ✓ Sample metadata: {list(sample.metadata.keys())}")
    
    # Test embeddings generator
    print("\n2. Testing GeminiEmbeddingsGenerator...")
    from data_ingestion.embeddings.gemini_embeddings import GeminiEmbeddingsGenerator
    
    try:
        generator = GeminiEmbeddingsGenerator()
        embedding = generator.generate_embedding("test text")
        print(f"   ✓ Generated embedding with dimension: {len(embedding)}")
    except Exception as e:
        print(f"   ✗ Embedding generation failed: {str(e)}")
    
    # Test entity extractor
    print("\n3. Testing EntityExtractor...")
    from data_ingestion.graph_builder.entity_extractor import EntityExtractor
    
    extractor = EntityExtractor()
    test_content = "Senior Python Developer with experience in Django, PostgreSQL, and AWS"
    entities = extractor.extract_entities(test_content, {'name': 'Test Person'})
    print(f"   ✓ Extracted entities: {dict(entities)}")


if __name__ == "__main__":
    print("Starting talent matching platform tests...\n")
    
    # Run individual component tests first
    asyncio.run(test_individual_components())
    
    # Then run full pipeline test
    asyncio.run(test_pipeline())
    
    print("\n" + "=" * 60)
    print("ALL TESTS COMPLETED")
    print("=" * 60)
    print("\nNext steps:")
    print("1. Start databases: task postgres:start && task neo4j:start")
    print("2. Run full ingestion: python data_ingestion/main.py")
    print("3. Start building the FastAPI backend")
