"""
Markdown document processor for extracting and chunking content
"""
import os
import re
from typing import List, Dict, Any
from pathlib import Path
import markdown
from dataclasses import dataclass


@dataclass
class DocumentChunk:
    """Represents a chunk of document content"""
    content: str
    metadata: Dict[str, Any]
    chunk_id: str
    source_file: str
    chunk_index: int


class MarkdownProcessor:
    """Processes markdown files and extracts structured content"""
    
    def __init__(self, chunk_size: int = 1000, chunk_overlap: int = 200):
        self.chunk_size = chunk_size
        self.chunk_overlap = chunk_overlap
    
    def process_file(self, file_path: str) -> List[DocumentChunk]:
        """Process a single markdown file and return chunks"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # Extract metadata from the document
            metadata = self._extract_metadata(content, file_path)
            
            # Clean and preprocess content
            cleaned_content = self._clean_content(content)
            
            # Create chunks
            chunks = self._create_chunks(cleaned_content, file_path, metadata)
            
            return chunks
            
        except Exception as e:
            print(f"Error processing file {file_path}: {str(e)}")
            return []
    
    def process_directory(self, directory_path: str) -> List[DocumentChunk]:
        """Process all markdown files in a directory"""
        all_chunks = []
        directory = Path(directory_path)
        
        for file_path in directory.glob("*.txt"):  # Assuming .txt files contain markdown
            chunks = self.process_file(str(file_path))
            all_chunks.extend(chunks)
        
        return all_chunks
    
    def _extract_metadata(self, content: str, file_path: str) -> Dict[str, Any]:
        """Extract metadata from document content"""
        metadata = {
            'source_file': os.path.basename(file_path),
            'file_path': file_path,
            'document_type': self._determine_document_type(file_path)
        }
        
        # Extract specific metadata based on document type
        if 'projects/' in file_path:
            metadata.update(self._extract_project_metadata(content))
        elif 'talent/' in file_path:
            metadata.update(self._extract_talent_metadata(content))
        elif 'resumes/' in file_path:
            metadata.update(self._extract_resume_metadata(content))
        
        return metadata
    
    def _determine_document_type(self, file_path: str) -> str:
        """Determine the type of document based on file path"""
        if 'projects/' in file_path:
            return 'project'
        elif 'talent/' in file_path:
            return 'talent'
        elif 'resumes/' in file_path:
            return 'resume'
        else:
            return 'unknown'
    
    def _extract_project_metadata(self, content: str) -> Dict[str, Any]:
        """Extract metadata specific to project documents"""
        metadata = {}
        
        # Extract project information
        project_id_match = re.search(r'Project ID:\s*(.+)', content)
        if project_id_match:
            metadata['project_id'] = project_id_match.group(1).strip()
        
        project_name_match = re.search(r'Project Name:\s*(.+)', content)
        if project_name_match:
            metadata['project_name'] = project_name_match.group(1).strip()
        
        status_match = re.search(r'Status:\s*(.+)', content)
        if status_match:
            metadata['status'] = status_match.group(1).strip()
        
        # Extract technical stack
        tech_stack = self._extract_technical_stack(content)
        if tech_stack:
            metadata['technical_stack'] = tech_stack
        
        return metadata
    
    def _extract_talent_metadata(self, content: str) -> Dict[str, Any]:
        """Extract metadata specific to talent documents"""
        metadata = {}
        
        # Extract basic info
        name_match = re.search(r'^(.+)\n', content)
        if name_match:
            metadata['name'] = name_match.group(1).strip()
        
        title_match = re.search(r'\n(.+)\nEmployee ID:', content)
        if title_match:
            metadata['title'] = title_match.group(1).strip()
        
        employee_id_match = re.search(r'Employee ID:\s*(.+)', content)
        if employee_id_match:
            metadata['employee_id'] = employee_id_match.group(1).strip()
        
        department_match = re.search(r'Department:\s*(.+)', content)
        if department_match:
            metadata['department'] = department_match.group(1).strip()
        
        # Extract skills
        skills = self._extract_skills(content)
        if skills:
            metadata['skills'] = skills
        
        return metadata
    
    def _extract_resume_metadata(self, content: str) -> Dict[str, Any]:
        """Extract metadata specific to resume documents"""
        metadata = {}
        
        # Extract basic info
        lines = content.split('\n')
        if lines:
            metadata['name'] = lines[0].strip()
        if len(lines) > 1:
            metadata['title'] = lines[1].strip()
        
        # Extract contact info
        email_match = re.search(r'Email:\s*(.+)', content)
        if email_match:
            metadata['email'] = email_match.group(1).strip()
        
        # Extract skills
        skills = self._extract_skills(content)
        if skills:
            metadata['skills'] = skills
        
        return metadata
    
    def _extract_technical_stack(self, content: str) -> List[str]:
        """Extract technical stack from content"""
        tech_stack = []
        
        # Look for technical stack section
        tech_section_match = re.search(r'TECHNICAL STACK\n-+\n(.*?)(?=\n[A-Z]|\n\n|\Z)', content, re.DOTALL)
        if tech_section_match:
            tech_content = tech_section_match.group(1)
            # Extract technologies mentioned
            tech_patterns = [
                r'Python', r'Java', r'JavaScript', r'TypeScript', r'Go', r'Scala',
                r'React', r'Vue\.js', r'Angular', r'Node\.js', r'Django', r'Flask',
                r'PostgreSQL', r'MongoDB', r'Redis', r'Elasticsearch',
                r'AWS', r'Azure', r'GCP', r'Docker', r'Kubernetes',
                r'TensorFlow', r'PyTorch', r'Spark', r'Kafka'
            ]
            
            for pattern in tech_patterns:
                if re.search(pattern, tech_content, re.IGNORECASE):
                    tech_stack.append(pattern)
        
        return tech_stack
    
    def _extract_skills(self, content: str) -> List[str]:
        """Extract skills from content"""
        skills = []
        
        # Look for skills section
        skills_section_match = re.search(r'TECHNICAL SKILLS\n(.*?)(?=\n[A-Z]|\n\n|\Z)', content, re.DOTALL)
        if skills_section_match:
            skills_content = skills_section_match.group(1)
            # Extract common technical skills
            skill_patterns = [
                r'Python', r'Java', r'JavaScript', r'TypeScript', r'Go', r'Scala', r'C\+\+',
                r'React', r'Vue', r'Angular', r'Node\.js', r'Django', r'Flask', r'FastAPI',
                r'PostgreSQL', r'MongoDB', r'Redis', r'MySQL', r'Elasticsearch',
                r'AWS', r'Azure', r'GCP', r'Docker', r'Kubernetes', r'Terraform',
                r'Machine Learning', r'Deep Learning', r'Data Science', r'AI',
                r'DevOps', r'CI/CD', r'Microservices', r'API'
            ]
            
            for pattern in skill_patterns:
                if re.search(pattern, skills_content, re.IGNORECASE):
                    skills.append(pattern.replace(r'\.', '.').replace(r'\+', '+'))
        
        return skills
    
    def _clean_content(self, content: str) -> str:
        """Clean and preprocess content"""
        # Remove excessive whitespace
        content = re.sub(r'\n\s*\n', '\n\n', content)
        content = re.sub(r' +', ' ', content)
        
        # Remove special characters that might interfere with processing
        content = content.strip()
        
        return content
    
    def _create_chunks(self, content: str, file_path: str, metadata: Dict[str, Any]) -> List[DocumentChunk]:
        """Create chunks from content"""
        chunks = []
        
        # Split content into paragraphs first
        paragraphs = content.split('\n\n')
        
        current_chunk = ""
        chunk_index = 0
        
        for paragraph in paragraphs:
            # If adding this paragraph would exceed chunk size, create a new chunk
            if len(current_chunk) + len(paragraph) > self.chunk_size and current_chunk:
                chunk_id = f"{os.path.basename(file_path)}_chunk_{chunk_index}"
                chunk_metadata = metadata.copy()
                chunk_metadata['chunk_index'] = chunk_index
                
                chunks.append(DocumentChunk(
                    content=current_chunk.strip(),
                    metadata=chunk_metadata,
                    chunk_id=chunk_id,
                    source_file=file_path,
                    chunk_index=chunk_index
                ))
                
                # Start new chunk with overlap
                if self.chunk_overlap > 0:
                    overlap_text = current_chunk[-self.chunk_overlap:]
                    current_chunk = overlap_text + "\n\n" + paragraph
                else:
                    current_chunk = paragraph
                
                chunk_index += 1
            else:
                if current_chunk:
                    current_chunk += "\n\n" + paragraph
                else:
                    current_chunk = paragraph
        
        # Add the last chunk if there's remaining content
        if current_chunk.strip():
            chunk_id = f"{os.path.basename(file_path)}_chunk_{chunk_index}"
            chunk_metadata = metadata.copy()
            chunk_metadata['chunk_index'] = chunk_index
            
            chunks.append(DocumentChunk(
                content=current_chunk.strip(),
                metadata=chunk_metadata,
                chunk_id=chunk_id,
                source_file=file_path,
                chunk_index=chunk_index
            ))
        
        return chunks
