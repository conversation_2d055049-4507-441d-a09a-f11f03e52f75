"""
Database manager for PostgreSQL and Neo4j connections
"""
import os
import asyncio
from typing import List, Dict, Any, Optional
import asyncpg
from neo4j import GraphDatabase
import json


class PostgreSQLManager:
    """Manages PostgreSQL database connections and operations"""
    
    def __init__(self, 
                 host: str = "localhost",
                 port: int = 5432,
                 database: str = "talent_matching",
                 user: str = "postgres",
                 password: str = "postgres"):
        self.host = host
        self.port = port
        self.database = database
        self.user = user
        self.password = password
        self.connection_pool = None
    
    async def initialize(self):
        """Initialize connection pool and create tables"""
        try:
            self.connection_pool = await asyncpg.create_pool(
                host=self.host,
                port=self.port,
                database=self.database,
                user=self.user,
                password=self.password,
                min_size=1,
                max_size=10
            )
            
            await self.create_tables()
            print("PostgreSQL initialized successfully")
            
        except Exception as e:
            print(f"Failed to initialize PostgreSQL: {str(e)}")
            raise
    
    async def create_tables(self):
        """Create necessary tables with pgvector extension"""
        async with self.connection_pool.acquire() as conn:
            # Enable pgvector extension
            await conn.execute("CREATE EXTENSION IF NOT EXISTS vector;")
            
            # Create documents table
            await conn.execute("""
                CREATE TABLE IF NOT EXISTS documents (
                    id SERIAL PRIMARY KEY,
                    chunk_id VARCHAR(255) UNIQUE NOT NULL,
                    content TEXT NOT NULL,
                    metadata JSONB,
                    source_file VARCHAR(255),
                    document_type VARCHAR(50),
                    chunk_index INTEGER,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                );
            """)
            
            # Create embeddings table
            await conn.execute("""
                CREATE TABLE IF NOT EXISTS embeddings (
                    id SERIAL PRIMARY KEY,
                    chunk_id VARCHAR(255) REFERENCES documents(chunk_id),
                    embedding_type VARCHAR(50),
                    embedding vector(384),
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                );
            """)
            
            # Create indexes
            await conn.execute("CREATE INDEX IF NOT EXISTS idx_documents_type ON documents(document_type);")
            await conn.execute("CREATE INDEX IF NOT EXISTS idx_documents_source ON documents(source_file);")
            await conn.execute("CREATE INDEX IF NOT EXISTS idx_embeddings_chunk ON embeddings(chunk_id);")
            await conn.execute("CREATE INDEX IF NOT EXISTS idx_embeddings_type ON embeddings(embedding_type);")
    
    async def insert_document(self, chunk_id: str, content: str, metadata: Dict[str, Any], 
                            source_file: str, document_type: str, chunk_index: int):
        """Insert a document chunk"""
        async with self.connection_pool.acquire() as conn:
            await conn.execute("""
                INSERT INTO documents (chunk_id, content, metadata, source_file, document_type, chunk_index)
                VALUES ($1, $2, $3, $4, $5, $6)
                ON CONFLICT (chunk_id) DO UPDATE SET
                    content = EXCLUDED.content,
                    metadata = EXCLUDED.metadata,
                    source_file = EXCLUDED.source_file,
                    document_type = EXCLUDED.document_type,
                    chunk_index = EXCLUDED.chunk_index
            """, chunk_id, content, json.dumps(metadata), source_file, document_type, chunk_index)
    
    async def insert_embedding(self, chunk_id: str, embedding_type: str, embedding: List[float]):
        """Insert an embedding"""
        async with self.connection_pool.acquire() as conn:
            await conn.execute("""
                INSERT INTO embeddings (chunk_id, embedding_type, embedding)
                VALUES ($1, $2, $3)
                ON CONFLICT (chunk_id, embedding_type) DO UPDATE SET
                    embedding = EXCLUDED.embedding
            """, chunk_id, embedding_type, embedding)
    
    async def search_similar_documents(self, query_embedding: List[float], 
                                     embedding_type: str = "local",
                                     limit: int = 10,
                                     document_type: Optional[str] = None) -> List[Dict[str, Any]]:
        """Search for similar documents using vector similarity"""
        async with self.connection_pool.acquire() as conn:
            query = """
                SELECT d.chunk_id, d.content, d.metadata, d.source_file, d.document_type,
                       e.embedding <-> $1 as distance
                FROM documents d
                JOIN embeddings e ON d.chunk_id = e.chunk_id
                WHERE e.embedding_type = $2
            """
            params = [query_embedding, embedding_type]
            
            if document_type:
                query += " AND d.document_type = $3"
                params.append(document_type)
            
            query += " ORDER BY e.embedding <-> $1 LIMIT $" + str(len(params) + 1)
            params.append(limit)
            
            rows = await conn.fetch(query, *params)
            
            results = []
            for row in rows:
                results.append({
                    'chunk_id': row['chunk_id'],
                    'content': row['content'],
                    'metadata': json.loads(row['metadata']) if row['metadata'] else {},
                    'source_file': row['source_file'],
                    'document_type': row['document_type'],
                    'similarity_score': 1 - row['distance']  # Convert distance to similarity
                })
            
            return results
    
    async def get_documents_by_type(self, document_type: str) -> List[Dict[str, Any]]:
        """Get all documents of a specific type"""
        async with self.connection_pool.acquire() as conn:
            rows = await conn.fetch("""
                SELECT chunk_id, content, metadata, source_file, document_type, chunk_index
                FROM documents
                WHERE document_type = $1
                ORDER BY source_file, chunk_index
            """, document_type)
            
            results = []
            for row in rows:
                results.append({
                    'chunk_id': row['chunk_id'],
                    'content': row['content'],
                    'metadata': json.loads(row['metadata']) if row['metadata'] else {},
                    'source_file': row['source_file'],
                    'document_type': row['document_type'],
                    'chunk_index': row['chunk_index']
                })
            
            return results
    
    async def close(self):
        """Close the connection pool"""
        if self.connection_pool:
            await self.connection_pool.close()


class Neo4jManager:
    """Manages Neo4j database connections and operations"""
    
    def __init__(self, 
                 uri: str = "bolt://localhost:7687",
                 user: str = "neo4j",
                 password: str = "password"):
        self.uri = uri
        self.user = user
        self.password = password
        self.driver = None
    
    def initialize(self):
        """Initialize Neo4j driver"""
        try:
            self.driver = GraphDatabase.driver(self.uri, auth=(self.user, self.password))
            self.create_constraints()
            print("Neo4j initialized successfully")
        except Exception as e:
            print(f"Failed to initialize Neo4j: {str(e)}")
            raise
    
    def create_constraints(self):
        """Create constraints and indexes"""
        with self.driver.session() as session:
            # Create constraints
            session.run("CREATE CONSTRAINT IF NOT EXISTS FOR (p:Person) REQUIRE p.id IS UNIQUE")
            session.run("CREATE CONSTRAINT IF NOT EXISTS FOR (proj:Project) REQUIRE proj.id IS UNIQUE")
            session.run("CREATE CONSTRAINT IF NOT EXISTS FOR (s:Skill) REQUIRE s.name IS UNIQUE")
            session.run("CREATE CONSTRAINT IF NOT EXISTS FOR (c:Company) REQUIRE c.name IS UNIQUE")
            session.run("CREATE CONSTRAINT IF NOT EXISTS FOR (t:Technology) REQUIRE t.name IS UNIQUE")
    
    def create_person_node(self, person_data: Dict[str, Any]):
        """Create a person node"""
        with self.driver.session() as session:
            session.run("""
                MERGE (p:Person {id: $id})
                SET p.name = $name,
                    p.title = $title,
                    p.email = $email,
                    p.type = $type,
                    p.department = $department
            """, **person_data)
    
    def create_project_node(self, project_data: Dict[str, Any]):
        """Create a project node"""
        with self.driver.session() as session:
            session.run("""
                MERGE (proj:Project {id: $id})
                SET proj.name = $name,
                    proj.status = $status,
                    proj.description = $description,
                    proj.budget = $budget
            """, **project_data)
    
    def create_skill_relationship(self, person_id: str, skill_name: str, proficiency: str = "intermediate"):
        """Create skill relationship"""
        with self.driver.session() as session:
            session.run("""
                MATCH (p:Person {id: $person_id})
                MERGE (s:Skill {name: $skill_name})
                MERGE (p)-[r:HAS_SKILL]->(s)
                SET r.proficiency = $proficiency
            """, person_id=person_id, skill_name=skill_name, proficiency=proficiency)
    
    def create_project_assignment(self, person_id: str, project_id: str, role: str):
        """Create project assignment relationship"""
        with self.driver.session() as session:
            session.run("""
                MATCH (p:Person {id: $person_id})
                MATCH (proj:Project {id: $project_id})
                MERGE (p)-[r:ASSIGNED_TO]->(proj)
                SET r.role = $role
            """, person_id=person_id, project_id=project_id, role=role)
    
    def find_people_with_skills(self, skills: List[str], limit: int = 10) -> List[Dict[str, Any]]:
        """Find people with specific skills"""
        with self.driver.session() as session:
            result = session.run("""
                MATCH (p:Person)-[r:HAS_SKILL]->(s:Skill)
                WHERE s.name IN $skills
                RETURN p.id as id, p.name as name, p.title as title, 
                       collect(s.name) as skills, count(s) as skill_count
                ORDER BY skill_count DESC
                LIMIT $limit
            """, skills=skills, limit=limit)
            
            return [dict(record) for record in result]
    
    def find_similar_projects(self, technologies: List[str], limit: int = 5) -> List[Dict[str, Any]]:
        """Find projects using similar technologies"""
        with self.driver.session() as session:
            result = session.run("""
                MATCH (proj:Project)-[r:USES_TECHNOLOGY]->(t:Technology)
                WHERE t.name IN $technologies
                RETURN proj.id as id, proj.name as name, proj.status as status,
                       collect(t.name) as technologies, count(t) as tech_count
                ORDER BY tech_count DESC
                LIMIT $limit
            """, technologies=technologies, limit=limit)
            
            return [dict(record) for record in result]
    
    def close(self):
        """Close the driver"""
        if self.driver:
            self.driver.close()
