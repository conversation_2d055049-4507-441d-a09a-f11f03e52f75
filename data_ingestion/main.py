"""
Main data ingestion pipeline for talent matching platform
"""
import asyncio
import os
from typing import List, Dict, Any
from pathlib import Path

from processors.markdown_processor import MarkdownProcessor
from embeddings.gemini_embeddings import GeminiEmbeddingsGenerator
from database_manager import <PERSON>greS<PERSON><PERSON>ana<PERSON>, Neo4j<PERSON>anager
from graph_builder.entity_extractor import EntityExtractor


class DataIngestionPipeline:
    """Main data ingestion pipeline"""
    
    def __init__(self, gemini_api_key: str = None):
        self.markdown_processor = MarkdownProcessor()
        self.embeddings_generator = GeminiEmbeddingsGenerator(gemini_api_key)
        self.postgres_manager = PostgreSQLManager()
        self.neo4j_manager = Neo4jManager()
        self.entity_extractor = EntityExtractor()
    
    async def initialize(self):
        """Initialize all components"""
        print("Initializing data ingestion pipeline...")
        
        await self.postgres_manager.initialize()
        self.neo4j_manager.initialize()
        
        print("Pipeline initialized successfully!")
    
    async def ingest_all_data(self):
        """Ingest all data from projects, talent, and resumes directories"""
        print("Starting data ingestion...")
        
        # Define data directories
        data_dirs = {
            'projects': 'projects',
            'talent': 'talent', 
            'resumes': 'resumes'
        }
        
        for doc_type, directory in data_dirs.items():
            if os.path.exists(directory):
                print(f"Processing {doc_type} documents...")
                await self.ingest_directory(directory, doc_type)
            else:
                print(f"Directory {directory} not found, skipping...")
        
        print("Data ingestion completed!")
    
    async def ingest_directory(self, directory_path: str, document_type: str):
        """Ingest all documents from a directory"""
        # Process markdown files
        chunks = self.markdown_processor.process_directory(directory_path)
        print(f"Processed {len(chunks)} chunks from {directory_path}")
        
        if not chunks:
            return
        
        # Generate embeddings
        print("Generating embeddings...")
        texts = [chunk.content for chunk in chunks]
        embeddings = self.embeddings_generator.generate_embeddings_batch(texts)
        
        # Store in PostgreSQL
        print("Storing in PostgreSQL...")
        for chunk, embedding in zip(chunks, embeddings):
            await self.postgres_manager.insert_document(
                chunk_id=chunk.chunk_id,
                content=chunk.content,
                metadata=chunk.metadata,
                source_file=chunk.source_file,
                document_type=document_type,
                chunk_index=chunk.chunk_index
            )
            
            await self.postgres_manager.insert_embedding(
                chunk_id=chunk.chunk_id,
                embedding_type="local",  # Using fallback model for now
                embedding=embedding
            )
        
        # Extract entities and build knowledge graph
        print("Building knowledge graph...")
        await self.build_knowledge_graph(chunks, document_type)
    
    async def build_knowledge_graph(self, chunks: List, document_type: str):
        """Build knowledge graph from document chunks"""
        for chunk in chunks:
            entities = self.entity_extractor.extract_entities(chunk.content, chunk.metadata)
            
            if document_type == 'talent':
                self._create_talent_graph_nodes(entities, chunk.metadata)
            elif document_type == 'projects':
                self._create_project_graph_nodes(entities, chunk.metadata)
            elif document_type == 'resumes':
                self._create_resume_graph_nodes(entities, chunk.metadata)
    
    def _create_talent_graph_nodes(self, entities: Dict[str, Any], metadata: Dict[str, Any]):
        """Create graph nodes for talent documents"""
        if 'name' in metadata:
            person_data = {
                'id': metadata.get('employee_id', metadata['name']),
                'name': metadata['name'],
                'title': metadata.get('title', ''),
                'email': metadata.get('email', ''),
                'type': 'internal',
                'department': metadata.get('department', '')
            }
            self.neo4j_manager.create_person_node(person_data)
            
            # Add skills
            if 'skills' in metadata:
                for skill in metadata['skills']:
                    self.neo4j_manager.create_skill_relationship(
                        person_data['id'], skill, 'advanced'
                    )
    
    def _create_project_graph_nodes(self, entities: Dict[str, Any], metadata: Dict[str, Any]):
        """Create graph nodes for project documents"""
        if 'project_id' in metadata:
            project_data = {
                'id': metadata['project_id'],
                'name': metadata.get('project_name', ''),
                'status': metadata.get('status', ''),
                'description': metadata.get('project_name', ''),
                'budget': metadata.get('budget', '')
            }
            self.neo4j_manager.create_project_node(project_data)
    
    def _create_resume_graph_nodes(self, entities: Dict[str, Any], metadata: Dict[str, Any]):
        """Create graph nodes for resume documents"""
        if 'name' in metadata:
            person_data = {
                'id': metadata['name'].replace(' ', '_').lower(),
                'name': metadata['name'],
                'title': metadata.get('title', ''),
                'email': metadata.get('email', ''),
                'type': 'external',
                'department': ''
            }
            self.neo4j_manager.create_person_node(person_data)
            
            # Add skills
            if 'skills' in metadata:
                for skill in metadata['skills']:
                    self.neo4j_manager.create_skill_relationship(
                        person_data['id'], skill, 'intermediate'
                    )
    
    async def search_documents(self, query: str, document_type: str = None, limit: int = 10):
        """Search documents using vector similarity"""
        # Generate query embedding
        query_embedding = self.embeddings_generator.generate_embedding(query)
        
        # Search in PostgreSQL
        results = await self.postgres_manager.search_similar_documents(
            query_embedding=query_embedding,
            embedding_type="local",
            limit=limit,
            document_type=document_type
        )
        
        return results
    
    async def close(self):
        """Close all connections"""
        await self.postgres_manager.close()
        self.neo4j_manager.close()


async def main():
    """Main function to run the ingestion pipeline"""
    # Get Gemini API key from environment or use provided key
    gemini_api_key = os.getenv("GEMINI_API_KEY", "AIzaSyB80zE6F7ibg2c-BXFrpKWfpIljoTJoqao")
    
    pipeline = DataIngestionPipeline(gemini_api_key)
    
    try:
        await pipeline.initialize()
        await pipeline.ingest_all_data()
        
        # Test search functionality
        print("\nTesting search functionality...")
        results = await pipeline.search_documents("machine learning engineer", limit=3)
        
        print(f"Found {len(results)} results:")
        for result in results:
            print(f"- {result['source_file']}: {result['similarity_score']:.3f}")
            print(f"  {result['content'][:100]}...")
            print()
    
    except Exception as e:
        print(f"Error in pipeline: {str(e)}")
    
    finally:
        await pipeline.close()


if __name__ == "__main__":
    asyncio.run(main())
