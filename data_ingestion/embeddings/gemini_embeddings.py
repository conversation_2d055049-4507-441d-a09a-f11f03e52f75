"""
Gemini AI embeddings generator for document chunks
"""
import os
import time
from typing import List, Dict, Any
import google.generativeai as genai
from sentence_transformers import SentenceTransformer
import numpy as np


class GeminiEmbeddingsGenerator:
    """Generate embeddings using Google Gemini AI"""
    
    def __init__(self, api_key: str = None, model_name: str = "models/embedding-001"):
        self.api_key = api_key or os.getenv("GEMINI_API_KEY")
        self.model_name = model_name
        
        if not self.api_key:
            raise ValueError("Gemini API key is required")
        
        # Configure Gemini
        genai.configure(api_key=self.api_key)
        
        # Fallback to sentence transformers if Gemini fails
        self.fallback_model = SentenceTransformer('all-MiniLM-L6-v2')
    
    def generate_embedding(self, text: str) -> List[float]:
        """Generate embedding for a single text"""
        try:
            # Use Gemini embedding
            result = genai.embed_content(
                model=self.model_name,
                content=text,
                task_type="retrieval_document"
            )
            return result['embedding']
        
        except Exception as e:
            print(f"Gemini embedding failed, using fallback: {str(e)}")
            # Use sentence transformers as fallback
            embedding = self.fallback_model.encode(text)
            return embedding.tolist()
    
    def generate_embeddings_batch(self, texts: List[str], batch_size: int = 10) -> List[List[float]]:
        """Generate embeddings for a batch of texts"""
        embeddings = []
        
        for i in range(0, len(texts), batch_size):
            batch = texts[i:i + batch_size]
            batch_embeddings = []
            
            for text in batch:
                embedding = self.generate_embedding(text)
                batch_embeddings.append(embedding)
                
                # Rate limiting - small delay between requests
                time.sleep(0.1)
            
            embeddings.extend(batch_embeddings)
            
            # Longer delay between batches
            if i + batch_size < len(texts):
                time.sleep(1)
        
        return embeddings
    
    def get_embedding_dimension(self) -> int:
        """Get the dimension of embeddings"""
        try:
            # Test with a small text to get dimension
            test_embedding = self.generate_embedding("test")
            return len(test_embedding)
        except:
            # Fallback model dimension
            return 384


class HybridEmbeddingsGenerator:
    """Hybrid embeddings generator using both Gemini and local models"""
    
    def __init__(self, gemini_api_key: str = None):
        self.gemini_generator = GeminiEmbeddingsGenerator(gemini_api_key)
        self.local_model = SentenceTransformer('all-MiniLM-L6-v2')
    
    def generate_embedding(self, text: str, use_gemini: bool = True) -> Dict[str, List[float]]:
        """Generate both Gemini and local embeddings"""
        embeddings = {}
        
        if use_gemini:
            try:
                embeddings['gemini'] = self.gemini_generator.generate_embedding(text)
            except Exception as e:
                print(f"Gemini embedding failed: {str(e)}")
        
        # Always generate local embedding as backup
        local_embedding = self.local_model.encode(text)
        embeddings['local'] = local_embedding.tolist()
        
        return embeddings
    
    def generate_embeddings_batch(self, texts: List[str], use_gemini: bool = True) -> List[Dict[str, List[float]]]:
        """Generate embeddings for a batch of texts"""
        embeddings = []
        
        for text in texts:
            text_embeddings = self.generate_embedding(text, use_gemini)
            embeddings.append(text_embeddings)
            
            # Rate limiting
            time.sleep(0.1)
        
        return embeddings


def cosine_similarity(embedding1: List[float], embedding2: List[float]) -> float:
    """Calculate cosine similarity between two embeddings"""
    vec1 = np.array(embedding1)
    vec2 = np.array(embedding2)
    
    dot_product = np.dot(vec1, vec2)
    norm1 = np.linalg.norm(vec1)
    norm2 = np.linalg.norm(vec2)
    
    if norm1 == 0 or norm2 == 0:
        return 0.0
    
    return dot_product / (norm1 * norm2)


def find_similar_embeddings(query_embedding: List[float], 
                          embeddings: List[List[float]], 
                          top_k: int = 5) -> List[tuple]:
    """Find most similar embeddings to query"""
    similarities = []
    
    for i, embedding in enumerate(embeddings):
        similarity = cosine_similarity(query_embedding, embedding)
        similarities.append((i, similarity))
    
    # Sort by similarity (descending)
    similarities.sort(key=lambda x: x[1], reverse=True)
    
    return similarities[:top_k]
