"""
Entity extractor for building knowledge graph
"""
import re
from typing import Dict, List, Any, Set


class EntityExtractor:
    """Extract entities and relationships from text content"""
    
    def __init__(self):
        # Define patterns for different entity types
        self.skill_patterns = [
            r'\b(?:Python|Java|JavaScript|TypeScript|Go|Scala|C\+\+|C#|Ruby|PHP|Swift|Kotlin)\b',
            r'\b(?:React|Vue\.js|Angular|Node\.js|Django|Flask|FastAPI|Spring|Express)\b',
            r'\b(?:PostgreSQL|MongoDB|Redis|MySQL|Elasticsearch|Cassandra|DynamoDB)\b',
            r'\b(?:AWS|Azure|GCP|Docker|Kubernetes|Terraform|Jenkins|Git)\b',
            r'\b(?:Machine Learning|Deep Learning|AI|Data Science|NLP|Computer Vision)\b',
            r'\b(?:DevOps|CI/CD|Microservices|API|REST|GraphQL|Agile|Scrum)\b'
        ]
        
        self.technology_patterns = [
            r'\b(?:TensorFlow|PyTorch|Scikit-learn|Keras|XGBoost|Pandas|NumPy)\b',
            r'\b(?:Apache Spark|Hadoop|Kafka|Airflow|Flink|Storm)\b',
            r'\b(?:Prometheus|Grafana|ELK Stack|DataDog|New Relic)\b',
            r'\b(?:OAuth|JWT|SSL|TLS|HTTPS|API Gateway)\b'
        ]
        
        self.company_patterns = [
            r'\b(?:Google|Microsoft|Amazon|Apple|Meta|Netflix|Uber|Airbnb)\b',
            r'\b(?:Stanford|MIT|Harvard|Berkeley|CMU|Caltech)\b'
        ]
    
    def extract_entities(self, content: str, metadata: Dict[str, Any]) -> Dict[str, Any]:
        """Extract entities from content and metadata"""
        entities = {
            'skills': self._extract_skills(content),
            'technologies': self._extract_technologies(content),
            'companies': self._extract_companies(content),
            'roles': self._extract_roles(content),
            'projects': self._extract_projects(content),
            'certifications': self._extract_certifications(content)
        }
        
        # Add metadata-based entities
        if 'skills' in metadata:
            entities['skills'].update(metadata['skills'])
        
        if 'technical_stack' in metadata:
            entities['technologies'].update(metadata['technical_stack'])
        
        return entities
    
    def _extract_skills(self, content: str) -> Set[str]:
        """Extract technical skills from content"""
        skills = set()
        
        for pattern in self.skill_patterns:
            matches = re.findall(pattern, content, re.IGNORECASE)
            skills.update(matches)
        
        # Clean up skills
        cleaned_skills = set()
        for skill in skills:
            cleaned_skill = skill.replace('\\', '').strip()
            if cleaned_skill:
                cleaned_skills.add(cleaned_skill)
        
        return cleaned_skills
    
    def _extract_technologies(self, content: str) -> Set[str]:
        """Extract technologies from content"""
        technologies = set()
        
        for pattern in self.technology_patterns:
            matches = re.findall(pattern, content, re.IGNORECASE)
            technologies.update(matches)
        
        return technologies
    
    def _extract_companies(self, content: str) -> Set[str]:
        """Extract company names from content"""
        companies = set()
        
        for pattern in self.company_patterns:
            matches = re.findall(pattern, content, re.IGNORECASE)
            companies.update(matches)
        
        # Also look for "Company:" pattern
        company_match = re.search(r'Company:\s*(.+)', content)
        if company_match:
            companies.add(company_match.group(1).strip())
        
        return companies
    
    def _extract_roles(self, content: str) -> Set[str]:
        """Extract job roles and titles from content"""
        roles = set()
        
        role_patterns = [
            r'\b(?:Senior|Principal|Lead|Staff|Director|VP|CTO|CEO)\s+(?:Engineer|Developer|Scientist|Manager|Architect)\b',
            r'\b(?:Software|Data|Machine Learning|DevOps|Backend|Frontend|Full Stack)\s+(?:Engineer|Developer|Scientist)\b',
            r'\b(?:Product|Project|Engineering|Technical)\s+(?:Manager|Lead|Director)\b'
        ]
        
        for pattern in role_patterns:
            matches = re.findall(pattern, content, re.IGNORECASE)
            roles.update(matches)
        
        return roles
    
    def _extract_projects(self, content: str) -> Set[str]:
        """Extract project names from content"""
        projects = set()
        
        # Look for project patterns
        project_patterns = [
            r'Project:\s*(.+)',
            r'Project Name:\s*(.+)',
            r'PROJECT:\s*(.+)'
        ]
        
        for pattern in project_patterns:
            matches = re.findall(pattern, content)
            for match in matches:
                project_name = match.strip()
                if project_name:
                    projects.add(project_name)
        
        return projects
    
    def _extract_certifications(self, content: str) -> Set[str]:
        """Extract certifications from content"""
        certifications = set()
        
        cert_patterns = [
            r'AWS Certified[^,\n]+',
            r'Google Cloud[^,\n]+',
            r'Microsoft Azure[^,\n]+',
            r'Certified[^,\n]+(?:Administrator|Engineer|Architect|Professional)',
            r'(?:CKA|CKS|CISSP|PMP|CISA|CISM)[^,\n]*'
        ]
        
        for pattern in cert_patterns:
            matches = re.findall(pattern, content, re.IGNORECASE)
            certifications.update(matches)
        
        return certifications
    
    def extract_relationships(self, entities: Dict[str, Any], metadata: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Extract relationships between entities"""
        relationships = []
        
        # Person-Skill relationships
        if 'name' in metadata and entities['skills']:
            person_id = metadata.get('employee_id', metadata['name'])
            for skill in entities['skills']:
                relationships.append({
                    'type': 'HAS_SKILL',
                    'source': person_id,
                    'target': skill,
                    'source_type': 'Person',
                    'target_type': 'Skill'
                })
        
        # Person-Company relationships
        if 'name' in metadata and entities['companies']:
            person_id = metadata.get('employee_id', metadata['name'])
            for company in entities['companies']:
                relationships.append({
                    'type': 'WORKED_AT',
                    'source': person_id,
                    'target': company,
                    'source_type': 'Person',
                    'target_type': 'Company'
                })
        
        # Project-Technology relationships
        if 'project_id' in metadata and entities['technologies']:
            project_id = metadata['project_id']
            for tech in entities['technologies']:
                relationships.append({
                    'type': 'USES_TECHNOLOGY',
                    'source': project_id,
                    'target': tech,
                    'source_type': 'Project',
                    'target_type': 'Technology'
                })
        
        return relationships
    
    def calculate_skill_proficiency(self, content: str, skill: str) -> str:
        """Calculate skill proficiency based on context"""
        skill_lower = skill.lower()
        content_lower = content.lower()
        
        # Look for proficiency indicators
        if any(word in content_lower for word in ['expert', 'advanced', 'senior', 'lead', 'principal']):
            if skill_lower in content_lower:
                return 'advanced'
        
        if any(word in content_lower for word in ['experienced', 'proficient', 'skilled']):
            if skill_lower in content_lower:
                return 'intermediate'
        
        if any(word in content_lower for word in ['junior', 'beginner', 'learning', 'basic']):
            if skill_lower in content_lower:
                return 'beginner'
        
        # Default to intermediate
        return 'intermediate'
