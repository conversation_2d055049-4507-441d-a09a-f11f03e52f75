version: '3'

vars:
  POSTGRES_DB: talent_matching
  POSTGRES_USER: postgres
  POSTGRES_PASSWORD: postgres
  POSTGRES_PORT: 5432
  NEO4J_USER: neo4j
  NEO4J_PASSWORD: password
  NEO4J_PORT: 7687
  NEO4J_HTTP_PORT: 7474

tasks:
  setup:
    desc: Setup the entire development environment
    cmds:
      - task: setup-postgres
      - task: setup-neo4j
      - task: install-deps
      - echo "Environment setup complete!"

  setup-postgres:
    desc: Setup PostgreSQL database with pgvector extension
    cmds:
      - echo "Setting up PostgreSQL with pgvector..."
      - podman run -d --name talent-postgres \
          -e POSTGRES_DB={{.POSTGRES_DB}} \
          -e POSTGRES_USER={{.POSTGRES_USER}} \
          -e POSTGRES_PASSWORD={{.POSTGRES_PASSWORD}} \
          -p {{.POSTGRES_PORT}}:5432 \
          -v postgres-data:/var/lib/postgresql/data \
          pgvector/pgvector:pg16
      - sleep 10
      - echo "PostgreSQL with pgvector is running on port {{.POSTGRES_PORT}}"

  setup-neo4j:
    desc: Setup Neo4j database
    cmds:
      - echo "Setting up Neo4j..."
      - podman run -d --name talent-neo4j \
          -e NEO4J_AUTH={{.NEO4J_USER}}/{{.NEO4J_PASSWORD}} \
          -p {{.NEO4J_PORT}}:7687 \
          -p {{.NEO4J_HTTP_PORT}}:7474 \
          -v neo4j-data:/data \
          -v neo4j-logs:/logs \
          neo4j:5.15
      - sleep 15
      - echo "Neo4j is running on ports {{.NEO4J_PORT}} (bolt) and {{.NEO4J_HTTP_PORT}} (http)"

  install-deps:
    desc: Install Python dependencies
    cmds:
      - echo "Installing Python dependencies..."
      - source venv/bin/activate && pip install --upgrade pip
      - source venv/bin/activate && pip install -r requirements.txt

  start-postgres:
    desc: Start PostgreSQL container
    cmds:
      - podman start talent-postgres || echo "PostgreSQL container already running"

  start-neo4j:
    desc: Start Neo4j container
    cmds:
      - podman start talent-neo4j || echo "Neo4j container already running"

  stop-postgres:
    desc: Stop PostgreSQL container
    cmds:
      - podman stop talent-postgres || echo "PostgreSQL container not running"

  stop-neo4j:
    desc: Stop Neo4j container
    cmds:
      - podman stop talent-neo4j || echo "Neo4j container not running"

  start-all:
    desc: Start all database containers
    cmds:
      - task: start-postgres
      - task: start-neo4j

  stop-all:
    desc: Stop all database containers
    cmds:
      - task: stop-postgres
      - task: stop-neo4j

  clean:
    desc: Remove all containers and volumes
    cmds:
      - podman stop talent-postgres talent-neo4j || true
      - podman rm talent-postgres talent-neo4j || true
      - podman volume rm postgres-data neo4j-data neo4j-logs || true
      - echo "All containers and volumes removed"

  status:
    desc: Check status of all containers
    cmds:
      - echo "Container status:"
      - podman ps -a --filter name=talent-

  logs-postgres:
    desc: Show PostgreSQL logs
    cmds:
      - podman logs talent-postgres

  logs-neo4j:
    desc: Show Neo4j logs
    cmds:
      - podman logs talent-neo4j

  psql:
    desc: Connect to PostgreSQL database
    cmds:
      - podman exec -it talent-postgres psql -U {{.POSTGRES_USER}} -d {{.POSTGRES_DB}}

  neo4j-shell:
    desc: Connect to Neo4j shell
    cmds:
      - podman exec -it talent-neo4j cypher-shell -u {{.NEO4J_USER}} -p {{.NEO4J_PASSWORD}}

  run-backend:
    desc: Run FastAPI backend
    cmds:
      - cd backend && source ../venv/bin/activate && uvicorn app.main:app --reload --host 0.0.0.0 --port 8000

  run-frontend:
    desc: Run Streamlit frontend
    cmds:
      - cd frontend && source ../venv/bin/activate && streamlit run app.py --server.port 8501

  run-ingestion:
    desc: Run data ingestion pipeline
    cmds:
      - source venv/bin/activate && python -m data_ingestion.main

  dev:
    desc: Start development environment (databases + backend + frontend)
    cmds:
      - task: start-all
      - echo "Starting backend and frontend..."
      - echo "Backend will be available at http://localhost:8000"
      - echo "Frontend will be available at http://localhost:8501"
      - echo "Neo4j browser available at http://localhost:7474"
